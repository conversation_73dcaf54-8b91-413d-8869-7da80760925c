"""
This module provides mock data for exporters when the real CSV data source is unavailable.
It generates realistic test data that matches the expected format and structure.
"""

import pandas as pd
import numpy as np
import datetime
import random

def generate_mock_data():
    """Generate mock data for testing exporters when CSV source is unavailable"""
    # Set random seed for reproducibility
    np.random.seed(42)
    random.seed(42)
    
    # Number of records to generate
    n_records = 300
    
    # Common data fields
    countries = ['USA', 'Canada', 'China', 'Japan', 'Germany', 'France', 'UK', 'Brazil', 'India', 'Australia']
    product_families = ['Medical Device', 'Pharma', 'Diagnostic', 'Therapeutic', 'Surgical']
    products = ['Product A', 'Product B', 'Product C', 'Product X', 'Product Y', 'Product Z']
    statuses = list(range(16))  # 0-15 status codes
    
    # Generate mock data
    data = {
        'productFamily': np.random.choice(product_families, n_records),
        'product': np.random.choice(products, n_records),
        'country': np.random.choice(countries, n_records),
        'status': np.random.choice(statuses, n_records),
        'sku': [f'SKU-{random.randint(1000, 9999)}' for _ in range(n_records)],
        'partNumber': [f'PART-{random.randint(100, 999)}' for _ in range(n_records)],
        'trackingId': [f'TRK-{random.randint(10000, 99999)}' for _ in range(n_records)],
        'skuDescription': [f'Description for SKU-{random.randint(1000, 9999)}' for _ in range(n_records)],
        'typeSubmission': np.random.choice(['Type A', 'Type B', 'Type C', 'Type D'], n_records),
        'comment': [f'Comment {i}' for i in range(n_records)],
        'trackingName': [f'Tracking {random.choice(["Alpha", "Beta", "Gamma", "Delta"])}' for _ in range(n_records)],
        'owner': np.random.choice(['John Smith', 'Jane Doe', 'Alice Johnson', 'Bob Williams'], n_records),
    }
    
    # Generate dates within a reasonable range
    current_date = datetime.datetime.now()
    
    # Various date fields
    date_fields = [
        'expectedApprovalDate', 'governmentDate', 'preparedDate', 'requestedDate', 
        'approvalDate', 'rejectedDate', 'submissionDate', 'checklistApprovalDate',
        'expiryDate'  # Add expiryDate field for tracking6.py
    ]
    
    for field in date_fields:
        # Generate random dates within past 2 years
        dates = [
            (current_date - datetime.timedelta(days=random.randint(0, 730))).strftime('%Y-%m-%d')
            for _ in range(n_records)
        ]
        data[field] = dates
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Add some nulls to make it realistic
    for col in df.columns:
        mask = np.random.random(n_records) < 0.1  # 10% nulls
        df.loc[mask, col] = np.nan
    
    return df
