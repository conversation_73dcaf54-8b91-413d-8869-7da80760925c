import uvicorn
from fastapi import FastAPI, staticfiles

from pdf_exporter.config import config
from pdf_exporter.routers import info, exporter

app = FastAPI()

app.mount('/public', staticfiles.StaticFiles(directory='public'), name='public')

for route in [info.router, exporter.router]:
    app.include_router(route)


def main():
    uvicorn.run(app, **config['http'])

if __name__ == '__main__':
    main()