from fastapi import APIRouter, Query, Path
from fastapi.responses import RedirectResponse
from pyinstrument import Profiler
from typing import Optional

from pdf_exporter.handlers.exporter import Handler
from pdf_exporter.progress_tracker import ProgressTracker

router = APIRouter(prefix='/exporter', tags=['exporter'])
handler = Handler()


@router.get('/{dashboard}', response_class=RedirectResponse)
async def export(
    dashboard: str = Path(description='dashboard name'), 
    source: int = Query(alias="from", description='source id'), 
    to: int = Query(description='client id'), 
    filename: str = Query(description='file name'),
    callback_url: Optional[str] = Query(None, description='Progress notification callback URL'),
    pdfId: Optional[str] = Query(None, description='PDF ID')
):
    # Initialize progress tracker if callback_url is provided
    tracker = ProgressTracker(dashboard, callback_url, pdfId) if callback_url else None
    
    profiler = Profiler()
    profiler.start()
    
    # Update initial progress if tracker exists
    if tracker:
        await tracker.update_progress(0, "initializing", "Starting PDF export request")
    
    result = await handler.export(dashboard, source, to, filename, tracker)
    
    profiler.stop()
    profiler.write_html('1.html')
    
    # Update final progress if tracker exists (not needed as base handler already does this)
    # if tracker:
    #     await tracker.update_progress(100, "completed", "PDF generation completed")
    
    return result
