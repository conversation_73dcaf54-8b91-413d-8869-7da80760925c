import asyncio
import time
import uuid
import aiohttp
import logging
from typing import Optional


class ProgressTracker:
    """
    Tracks progress of PDF generation tasks and notifies an external callback URL.
    
    This class handles sending progress notifications to an external service
    during the PDF generation process.
    """
    
    def __init__(self, task_name: str, callback_url: Optional[str] = None, pdfId: Optional[str] = None):
        """
        Initialize a new progress tracker.
        
        Args:
            task_name: Name of the task (e.g., dashboard type)
            callback_url: Optional URL to send progress notifications to
        """
        self.task_id = f"{task_name}_{int(time.time())}_{str(uuid.uuid4())[:8]}"
        self.callback_url = callback_url
        self.current_progress = 0
        self.logger = logging.getLogger("progress_tracker")
        self.pdfId = pdfId

    async def update_progress(self, progress: int, status: str, message: str, file: Optional[str] = None) -> None:
        """
        Update the current progress and send a notification if callback_url is set.
        
        Args:
            progress: Current progress (0-100)
            status: Current status (e.g., "downloading", "processing")
            message: Descriptive message about the current status
            file: Optional file path associated with the progress update
        """
        self.current_progress = progress

        self.logger.info(f"Task {self.task_id}: {progress}% - {status} - {message}")
        
        if self.callback_url:
            await self._send_notification(progress, status, message, file)

    async def _send_notification(self, progress: int, status: str, message: str, file: Optional[str] = None) -> None:
        """
        Send a progress notification to the callback URL.
        
        This method is non-blocking and doesn't raise exceptions to the caller,
        ensuring the main processing flow isn't affected by notification failures.
        
        Args:
            progress: Current progress (0-100)
            status: Current status
            message: Descriptive message
        """
        payload = {
            "task_id": self.task_id,
            "pdfId": self.pdfId,
            "progress": progress,
            "status": status,
            "message": message,
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
            "secret": "NO8dunrgI6W0peP711yxr0LxGupOgLSPel52XW3dnoBoXYKiOsOl9ZDlojE6sqK53sm"
        }

        if file:
            payload["file"] = file

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.callback_url, 
                    json=payload, 
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    response_text = await response.text()
                    if response.status != 200:
                        self.logger.warning(
                            f"Failed to send progress notification. Status: {response.status}, Response: {response_text}"
                        )
                    else:
                        self.logger.info(f"Progress notification sent successfully: {progress}% - {status}")
        except Exception as e:
            self.logger.warning(f"Error sending progress notification: {str(e)}")
