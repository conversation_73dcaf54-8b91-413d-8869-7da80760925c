import pandas as pd
from typing import Optional, List

from pdf_exporter.handlers.exporters.base import Base
from pdf_exporter.config import config
from pdf_exporter.progress_tracker import ProgressTracker


class Handler(Base):

    PANELS = [61, 62, 63, 64, 65]

    async def _build_tables(self, source: int, to: int, tracker: Optional[ProgressTracker] = None):
        """Build table PDFs with optional progress tracking"""
        # Start data loading - progress 10-15%
        if tracker:
            await tracker.update_progress(10, "loading_data", "正在加载CSV数据...")
            
        df = self._load_data(source, to)
        
        if tracker:
            await tracker.update_progress(15, "processing_data", "正在处理数据...")
            
        # Check if we have data to process
        if df.empty:
            print("Warning: No data available for the requested time range")
            
            # Generate a blank PDF with a message instead of returning an empty list
            message_pdf = f"{self.dir}/empty.pdf"
            self._create_empty_pdf(message_pdf, "No data available for the requested time range")
            
            if tracker:
                await tracker.update_progress(35, "tables_completed", "无数据可用")
                
            return [message_pdf]
        
        # Build first set of tables - progress 15-25%
        if tracker:
            await tracker.update_progress(15, "building_tables", "正在处理第一部分表格...")
        
        table1_files = await self.__build1(df.copy(), tracker)
        
        # Build second set of tables - progress 25-35% 
        if tracker:
            await tracker.update_progress(25, "building_tables", "正在处理第二部分表格...")
        
        table2_files = await self.__build2(df.copy(), tracker)
        
        if tracker:
            await tracker.update_progress(35, "tables_completed", "表格处理完成")
            
        return table1_files + table2_files
        
    def _create_empty_pdf(self, filename, message="No data available"):
        """Create a simple PDF with a message for empty data cases"""
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.platypus import Paragraph, Table
        
        styles = getSampleStyleSheet()
        style = styles['Heading1']
        
        # Create a paragraph with the message
        p = Paragraph(message, style)
        
        # Build PDF with the message
        self._build_pdf(filename, [p])

    async def __build1(self, df, tracker: Optional[ProgressTracker] = None):
        """Build first part of tables with progress tracking"""
        input_files = []
        
        # Process data
        if tracker:
            await tracker.update_progress(16, "processing_data", "正在处理第一部分数据...")
            
        df = df[['country', 'productFamily', 'product', 'sku', 'partNumber', 'trackingName', 'status', 'trackingId']]
        df = df.dropna(subset=['trackingId', 'country', 'status'], how='any')
        
        # Handle date conversions with robust error handling to prevent overflow
        def safe_date_convert(date_series, column_name):
            try:
                # First convert to datetime with coerce
                converted = pd.to_datetime(date_series, errors='coerce')
                
                # Filter out extreme dates that might cause overflow
                # Keep only dates between year 1900 and 2100
                mask = (converted.dt.year >= 1900) & (converted.dt.year <= 2100)
                converted.loc[~mask] = pd.NaT
                
                # Convert to string format safely
                return converted.dt.strftime('%Y-%m-%d').fillna('N/A')
            except Exception as e:
                print(f"Warning: Error converting {column_name}: {{str(e)}}")
                # Return all N/A values if conversion fails
                return pd.Series(['N/A'] * len(date_series), index=date_series.index)
        
        # Note: expectedApprovalDate is only used in __build2, not here in __build1


        df.fillna({'productFamily': '', 'product': '', 'sku': 'Not Set', 'partNumber': 'Not Set', 'trackingName': 'Not Set'}, inplace=True)
        df['productFamily'] = df['productFamily'].astype(str).apply(lambda x: self._cut_str(x, .15))
        df['product'] = df['product'].astype(str).apply(lambda x: self._cut_str(x, .15))
        df['sku'] = df['sku'].astype(str).apply(lambda x: self._cut_str(x, .15))
        df['partNumber'] = df['partNumber'].astype(str).apply(lambda x: self._cut_str(x, .15))
        df['trackingName'] = df['trackingName'].astype(str).apply(lambda x: self._cut_str(x, .1))

        df['status'] = df['status'].map(self._get_status)

        df = df.sort_values(['country', 'trackingId', 'productFamily', 'product'])
        df = df.reset_index(drop=['index'])
        
        if tracker:
            await tracker.update_progress(18, "generating_pdfs", "正在生成PDF表格 (第一部分)...")

        # Generate PDFs with progress tracking
        segments = [df.iloc[seg:seg + self.SEG_SIZE] for seg in range(0, len(df), self.SEG_SIZE)]
        for index, item in enumerate(segments):
            # Update progress incrementally from 18% to 25%
            if tracker and len(segments) > 0:
                progress = 18 + (index / len(segments)) * 7
                await tracker.update_progress(
                    int(progress), 
                    "generating_pdfs", 
                    f"正在生成PDF表格 (第一部分 {index+1}/{len(segments)})..."
                )
                
            file = f"{self.dir}/1-{index}.pdf"
            input_files.append(file)
            self.__generate_pdf1(item, file)

        return input_files

    def __generate_pdf1(self, df, filename="output.pdf"):
        country_groupd = self._get_indexes(df.groupby(['country'], sort=False).size())
        trackingId_groupd = self._get_indexes(df.groupby(['trackingId'], sort=False).size())
        productFamily_groupd = self._get_indexes(df.groupby(['trackingId', 'productFamily'], sort=False).size())
        product_groupd = self._get_indexes(df.groupby(['trackingId', 'productFamily', 'product'], sort=False).size())
        sku_groupd = self._get_indexes(df.groupby(['trackingId', 'productFamily', 'product', 'sku'], sort=False).size())

        styles = []
        for row in country_groupd:
            styles.append(('SPAN', (0, row[0] + 1), (0, row[1] + 1)))
        for row in productFamily_groupd:
            styles.append(('SPAN', (1, row[0] + 1), (1, row[1] + 1)))
        for row in product_groupd:
            styles.append(('SPAN', (2, row[0] + 1), (2, row[1] + 1)))
        for row in sku_groupd:
            styles.append(('SPAN', (3, row[0] + 1), (3, row[1] + 1)))
            styles.append(('SPAN', (4, row[0] + 1), (4, row[1] + 1)))
            styles.append(('SPAN', (5, row[0] + 1), (5, row[1] + 1)))
        for row in trackingId_groupd:
            styles.append(('SPAN', (6, row[0] + 1), (6, row[1] + 1)))
            
        # Add additional styles to reduce vertical spacing
        styles.append(('LEFTPADDING', (0, 0), (-1, -1), 2))  # Reduce left padding
        styles.append(('RIGHTPADDING', (0, 0), (-1, -1), 2)) # Reduce right padding
        styles.append(('TOPPADDING', (0, 0), (-1, -1), 2))   # Reduce top padding
        styles.append(('BOTTOMPADDING', (0, 0), (-1, -1), 2)) # Reduce bottom padding
        styles.append(('FONTSIZE', (0, 0), (-1, -1), 7))     # Smaller font size
        
        df.drop(columns=['trackingId'], inplace=True)
        data = df.values.tolist()
        data.insert(0, self._format_headers(df.columns.tolist()))
        # Use custom _build_compact_table method with dynamic row heights
        table = self._build_compact_table(data, col=[.2, .2, .1, .15, .2, .15], styles=styles)
        self._build_pdf(filename, [table])
        
    def _build_compact_table(self, data, col=None, styles=[], row_heights=None):
        """Build a compact table with dynamic row heights based on content"""
        from reportlab.platypus import Table, TableStyle
        from reportlab.lib.units import inch
        
        total_width = self.pagesize[0] - 2*inch
        style = TableStyle(styles + self.styles)
        colWidths = [item * total_width for item in col] if col is not None else None
        
        # Calculate appropriate row heights based on content if not provided
        if not row_heights:
            row_heights = []
            for row_idx, row in enumerate(data):
                # Header row gets slightly more space
                if row_idx == 0:
                    row_heights.append(12)
                    continue
                
                # For data rows, calculate height based on content
                max_height = 0
                for cell in row:
                    # Count approximate number of lines needed for this cell
                    if isinstance(cell, str):
                        # Calculate how many lines this text would need
                        text_len = len(cell)
                        # Rough estimate - assume 10 chars per line for each 10% of column width
                        # This is a simple approximation that can be adjusted
                        cell_col_idx = row.index(cell)
                        if cell_col_idx < len(col) and col:
                            col_width_factor = col[cell_col_idx]
                            chars_per_line = int(col_width_factor * 100)
                            lines = max(1, text_len / chars_per_line) if chars_per_line > 0 else 1
                            # Calculate height: base 8pt font with 2pt line spacing
                            cell_height = int(8 * lines) + 2
                            max_height = max(max_height, cell_height)
                
                # Set minimum height and avoid excessive heights
                row_heights.append(max(10, min(max_height, 30)))
        
        # Add special style to enable auto word wrapping
        # The style command needs to be in the correct format for ReportLab
        if ('WORDWRAP', (0, 0), (-1, -1)) not in style._cmds:
            style.add('WORDWRAP', (0, 0), (-1, -1))
        
        # Use Table instead of LongTable for more compact layout with dynamic row heights
        table = Table(data, 
                     repeatRows=1, 
                     colWidths=colWidths, 
                     rowHeights=row_heights,
                     style=style)
        
        return table
        
    async def __build2(self, df, tracker: Optional[ProgressTracker] = None):
        """Build second part of tables with progress tracking"""
        input_files = []
        
        # Process data
        if tracker:
            await tracker.update_progress(26, "processing_data", "正在处理第二部分数据...")
            
        df = df[['country', 'productFamily', 'product', 'sku', 'skuDescription', 'typeSubmission', 'expectedApprovalDate', 'comment', 'trackingId']]
        df = df.dropna(subset=['trackingId', 'country'], how='any')

        # Convert date with proper error handling
        try:
            # Convert to datetime with proper error handling and explicit format
            # ISO format is used for standardized date parsing
            expected_approval_dt = pd.to_datetime(df['expectedApprovalDate'], format='%Y-%m-%d', errors='coerce')
            
            # Filter out extreme dates that might cause overflow
            # Keep only dates between year 1900 and 2100
            date_mask = (expected_approval_dt.dt.year >= 1900) & (expected_approval_dt.dt.year <= 2100)
            expected_approval_dt.loc[~date_mask] = pd.NaT
            
            # Format dates for display
            df['expectedApprovalDate'] = expected_approval_dt.dt.strftime('%Y-%m-%d').fillna('N/A')
            
        except Exception as e:
            print(f"Warning: Error converting expectedApprovalDate: {str(e)}")
            # Fallback to string representation
            df['expectedApprovalDate'] = df['expectedApprovalDate'].astype(str).fillna('N/A')
            
        df.fillna({'productFamily': '', 'product': '', 'sku': 'Not Set', 'skuDescription': '',
                  'typeSubmission': '', 'expectedApprovalDate': 'N/A', 'comment': ''}, inplace=True)
        df['productFamily'] = df['productFamily'].astype(str).apply(lambda x: self._cut_str(x, .1))
        df['product'] = df['product'].astype(str).apply(lambda x: self._cut_str(x, .1))
        df['sku'] = df['sku'].astype(str).apply(lambda x: self._cut_str(x, .1))
        df['skuDescription'] = df['skuDescription'].astype(str).apply(lambda x: self._cut_str(x, .1))
        df['typeSubmission'] = df['typeSubmission'].astype(str).apply(lambda x: self._cut_str(x, .15))
        df['comment'] = df['comment'].astype(str).apply(lambda x: self._cut_str(x, .1))
        df = df.reset_index(drop=True)
        
        if tracker:
            await tracker.update_progress(28, "generating_pdfs", "正在生成PDF表格 (第二部分)...")

        # Generate PDFs with progress tracking
        segments = [df.iloc[seg:seg + self.SEG_SIZE] for seg in range(0, len(df), self.SEG_SIZE)]
        for index, item in enumerate(segments):
            # Update progress incrementally from 28% to 35%
            if tracker and len(segments) > 0:
                progress = 28 + (index / len(segments)) * 7
                await tracker.update_progress(
                    int(progress), 
                    "generating_pdfs", 
                    f"正在生成PDF表格 (第二部分 {index+1}/{len(segments)})..."
                )
                
            file = f"{self.dir}/2-{index}.pdf"
            try:
                self.__generate_pdf2(item, file)
                input_files.append(file)
            except Exception as e:
                print(f"Error generating PDF segment {index}: {str(e)}")
                # Continue with next segment instead of failing completely

        return input_files

    def __generate_pdf2(self, df, filename="output.pdf"):
        country_groupd = self._get_indexes(df.groupby(['country'], sort=False).size())
        trackingId_groupd = self._get_indexes(df.groupby(['trackingId'], sort=False).size())
        productFamily_groupd = self._get_indexes(df.groupby(['trackingId', 'productFamily'], sort=False).size())
        product_groupd = self._get_indexes(df.groupby(['trackingId', 'productFamily', 'product'], sort=False).size())
        sku_groupd = self._get_indexes(df.groupby(['trackingId', 'productFamily', 'product', 'sku'], sort=False).size())

        styles = []
        for row in country_groupd:
            styles.append(('SPAN', (0, row[0] + 1), (0, row[1] + 1)))
        for row in productFamily_groupd:
            styles.append(('SPAN', (1, row[0] + 1), (1, row[1] + 1)))
        for row in product_groupd:
            styles.append(('SPAN', (2, row[0] + 1), (2, row[1] + 1)))
        for row in sku_groupd:
            styles.append(('SPAN', (3, row[0] + 1), (3, row[1] + 1)))
            styles.append(('SPAN', (4, row[0] + 1), (4, row[1] + 1)))
            styles.append(('SPAN', (5, row[0] + 1), (5, row[1] + 1)))
        for row in trackingId_groupd:
            styles.append(('SPAN', (6, row[0] + 1), (6, row[1] + 1)))
            styles.append(('SPAN', (7, row[0] + 1), (7, row[1] + 1)))

        # Add additional styles to reduce vertical spacing
        styles.append(('LEFTPADDING', (0, 0), (-1, -1), 2))  # Reduce left padding
        styles.append(('RIGHTPADDING', (0, 0), (-1, -1), 2)) # Reduce right padding
        styles.append(('TOPPADDING', (0, 0), (-1, -1), 2))   # Reduce top padding
        styles.append(('BOTTOMPADDING', (0, 0), (-1, -1), 2)) # Reduce bottom padding
        styles.append(('FONTSIZE', (0, 0), (-1, -1), 7))     # Smaller font size

        df.drop(columns=['trackingId'], inplace=True)
        data = df.values.tolist()
        data.insert(0, self._format_headers(df.columns.tolist()))
        # Use custom _build_compact_table method with dynamic row heights
        table = self._build_compact_table(data, col=[.2, .2, .1, .15, .2, .15], styles=styles)
        self._build_pdf(filename, [table])
