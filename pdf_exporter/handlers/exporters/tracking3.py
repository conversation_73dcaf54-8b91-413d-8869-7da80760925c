import pandas as pd
import time
import aiohttp
from typing import Optional, List

from reportlab.platypus import Image, Spacer, Table
from reportlab.lib.units import inch

from pdf_exporter.handlers.exporters.base import Base
from pdf_exporter.config import config
from pdf_exporter.progress_tracker import ProgressTracker


class Handler(Base):

    PANELS = [9, 10, 11, 12]
    
    async def export(self, source: int, to: int, filename: str = None, tracker: Optional[ProgressTracker] = None):
        """Override the export method to place tables before charts with progress tracking"""
        # Start loading data - progress 10%
        if tracker:
            await tracker.update_progress(10, "loading_data", "正在加载数据...")
            
        # Now await the async method and pass the tracker
        input_files = await self._build_tables(source, to, tracker)
        
        # Start downloading charts - progress 40%
        if tracker:
            await tracker.update_progress(40, "downloading_charts", "正在下载图表...")
            
        # Get charts after tables
        charts = await self._build_charts(source, to, tracker)
        
        # Place tables first, then charts (reversed order from base class)
        input_files = input_files + charts
        
        # Use two parameters for string formatting as required by self.OUTPUT pattern
        timestamp = str(int(time.time()))
        output_file = self.OUTPUT.format(timestamp, filename if filename else timestamp)
        self._merge_pdf(input_files, output_file)
        import shutil
        shutil.rmtree(self.dir)
        return f"/{output_file}"
        
    async def _build_charts(self, source: int, to: int, tracker: Optional[ProgressTracker] = None):
        """Override the _build_charts method to include enhanced error handling and progress tracking"""
        if len(self.PANELS) < 1:
            return []
            
        import logging
        story = []
        images = []
        total_panels = len(self.PANELS)
        
        # Download images with better error handling
        for i, panel_id in enumerate(self.PANELS):
            # Update progress incrementally from 45% to 60% based on download progress
            if tracker and total_panels > 0:
                progress = 45 + (i / total_panels) * 15
                await tracker.update_progress(
                    int(progress), 
                    "downloading_charts", 
                    f"正在下载图表 ({i+1}/{total_panels})..."
                )
                
            try:
                # Use the parent class method to download individual panel
                panel_images = await self.__download_panel_image(panel_id, source, to)
                if panel_images:
                    images.extend(panel_images)
                else:
                    print(f"Warning: Panel {panel_id} returned no image")
            except Exception as e:
                print(f"Error downloading panel {panel_id}: {str(e)}")
        
        # Build PDF with available images
        for image in images:
            story.append(Image(image, width=self.pagesize[0] - 2 * inch, height=self.HRIGHT / self.WIDTH * (self.pagesize[0] - 2 * inch)))
            story.append(Spacer(1, 0.5*inch))
            
        if story:
            self._build_pdf(f"{self.dir}/{self.CHART_PDF}", story, topMargin=2 * inch, bottomMargin=1.5 * inch)
            return [f"{self.dir}/{self.CHART_PDF}"]
        return []
        
    async def __download_panel_image(self, panel_id, source, to):
        """Helper method to download a single panel image with better error handling"""
        import aiohttp
        from reportlab.platypus import Image, Spacer, Table
        from reportlab.lib.units import inch
        
        images = []
        connector = aiohttp.TCPConnector(limit=self.DOWNLOAD_LIMIT)
        
        url = f"{config['grafana']['url']}{self.DASHBOARD}"
        params = {
            'orgId': 1,
            'theme': self.THEME,
            'width': self.WIDTH,
            'height': self.HRIGHT,
            'from': source,
            'to': to,
            'panelId': f"panel-{panel_id}"
        }
        
        image_path = f"{self.dir}/{panel_id}.png"
        
        try:
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.get(url, headers=self.HEADERS, params=params) as response:
                    if response.status != 200:
                        print(f"Panel {panel_id} returned status code {response.status}")
                        return []
                        
                    img_data = await response.read()
                    
                    # Check if we got valid image data
                    if len(img_data) < 100:  # Very small response is likely an error
                        print(f"Panel {panel_id} returned very small response: {img_data}")
                        return []
                        
                    with open(image_path, 'wb') as f:
                        f.write(img_data)
                        
                    return [image_path]
        except Exception as e:
            print(f"Exception while downloading panel {panel_id}: {str(e)}")
            return []

    async def _build_tables(self, source: int, to: int, tracker: Optional[ProgressTracker] = None):
        """Build table PDFs with progress tracking"""
        input_files = []
        
        # Start loading data - update progress if tracker exists
        if tracker:
            await tracker.update_progress(15, "processing_data", "正在处理数据...")
            
        df = self._load_data(source, to)

        df = df[['productFamily', 'product', 'country', 'status', 'sku', 'partNumber', 'trackingId']]
        df = df.dropna(subset=['trackingId', 'country', 'status'], how='any')
        df.fillna({'productFamily': '', 'product': '', 'sku': '', 'partNumber': 'Not Set'}, inplace=True)
        df['productFamily'] = df['productFamily'].astype(str).apply(lambda x: self._cut_str(x, .2))
        df['product'] = df['product'].astype(str).apply(lambda x: self._cut_str(x, .2))
        df['sku'] = df['sku'].astype(str).apply(lambda x: self._cut_str(x, .2))
        
        df['status'] = df['status'].map(self._get_status)
        df = df.sort_values(['country', 'trackingId', 'productFamily', 'product'])
        df = df.reset_index(drop=True)  # Changed from drop=['index'] to drop=True

        # Generate PDFs with progress tracking
        if tracker:
            await tracker.update_progress(20, "generating_pdfs", "正在生成PDF表格...")
            
        segments = [df.iloc[seg:seg + self.SEG_SIZE] for seg in range(0, len(df), self.SEG_SIZE)]
        for index, item in enumerate(segments):
            # Update progress incrementally from 20% to 35%
            if tracker and len(segments) > 0:
                progress = 20 + (index / len(segments)) * 15
                await tracker.update_progress(
                    int(progress), 
                    "generating_pdfs", 
                    f"正在生成PDF表格 ({index+1}/{len(segments)})..."
                )
                
            file = f"{self.dir}/{index}.pdf"
            self.generate_pdf(item, file)
            input_files.append(file)
            
        return input_files

    def generate_pdf(self, df, filename="output.pdf"):
        country_groupd = self._get_indexes(df.groupby('country', sort=False).size())
        trackingId_groupd = self._get_indexes(df.groupby('trackingId', sort=False).size())
        productFamily_groupd = self._get_indexes(df.groupby(['trackingId', 'productFamily'], sort=False).size())
        product_groupd = self._get_indexes(df.groupby(['trackingId', 'productFamily', 'product'], sort=False).size())
        sku_groupd = self._get_indexes(df.groupby(['trackingId', 'productFamily', 'product', 'sku'], sort=False).size())

        styles = []
        # Add additional styles to reduce vertical spacing
        styles.append(('LEFTPADDING', (0, 0), (-1, -1), 2))  # Reduce left padding
        styles.append(('RIGHTPADDING', (0, 0), (-1, -1), 2)) # Reduce right padding
        styles.append(('TOPPADDING', (0, 0), (-1, -1), 2))   # Reduce top padding
        styles.append(('BOTTOMPADDING', (0, 0), (-1, -1), 2)) # Reduce bottom padding
        styles.append(('FONTSIZE', (0, 0), (-1, -1), 7))     # Smaller font size
        for row in productFamily_groupd:
            styles.append(('SPAN', (0, row[0] + 1), (0, row[1] + 1)))
        for row in product_groupd:
            styles.append(('SPAN', (1, row[0] + 1), (1, row[1] + 1)))
            styles.append(('SPAN', (3, row[0] + 1), (3, row[1] + 1)))
        for row in sku_groupd:
            styles.append(('SPAN', (4, row[0] + 1), (4, row[1] + 1)))
        for row in country_groupd:
            styles.append(('SPAN', (2, row[0] + 1), (2, row[1] + 1)))
        for row in trackingId_groupd:
            styles.append(('SPAN', (5, row[0] + 1), (5, row[1] + 1)))

        df.drop(columns=['trackingId'], inplace=True)
        data = df.values.tolist()
        data.insert(0, df.columns.tolist())  # Fixed height for all rows
        table = self._build_compact_table(data, col=[.2, .2, .1, .2, .15, .15], styles=styles)
        self._build_pdf(filename, [table])

    def _build_compact_table(self, data, col=None, styles=[], row_heights=None):
        """Build a compact table with dynamic row heights based on content"""
        from reportlab.platypus import Table, TableStyle
        from reportlab.lib.units import inch
        
        total_width = self.pagesize[0] - 2*inch
        style = TableStyle(styles + self.styles)
        colWidths = [item * total_width for item in col] if col is not None else None
        
        # Calculate appropriate row heights based on content if not provided
        if not row_heights:
            row_heights = []
            for row_idx, row in enumerate(data):
                # Header row gets slightly more space
                if row_idx == 0:
                    row_heights.append(12)
                    continue
                
                # For data rows, calculate height based on content
                max_height = 0
                for cell in row:
                    # Count approximate number of lines needed for this cell
                    if isinstance(cell, str):
                        # Calculate how many lines this text would need
                        text_len = len(cell)
                        # Rough estimate - assume 10 chars per line for each 10% of column width
                        # This is a simple approximation that can be adjusted
                        cell_col_idx = row.index(cell)
                        if cell_col_idx < len(col) and col:
                            col_width_factor = col[cell_col_idx]
                            chars_per_line = int(col_width_factor * 100)
                            lines = max(1, text_len / chars_per_line) if chars_per_line > 0 else 1
                            # Calculate height: base 8pt font with 2pt line spacing
                            cell_height = int(8 * lines) + 2
                            max_height = max(max_height, cell_height)
                
                # Set minimum height and avoid excessive heights
                row_heights.append(max(10, min(max_height, 30)))
        
        # Add special style to enable auto word wrapping
        # The style command needs to be in the correct format for ReportLab
        if ('WORDWRAP', (0, 0), (-1, -1)) not in style._cmds:
            style.add('WORDWRAP', (0, 0), (-1, -1))
        
        # Use Table instead of LongTable for more compact layout with dynamic row heights
        table = Table(data, 
                     repeatRows=1, 
                     colWidths=colWidths, 
                     rowHeights=row_heights,
                     style=style)
        
        return table
        