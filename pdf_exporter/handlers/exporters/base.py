import time
from abc import ABC, abstractmethod
import os
import asyncio
from pypdf import PdfReader, PdfWriter
import aiohttp
import shutil
import pandas as pd
from typing import Optional, List


from reportlab.platypus import Image, Table
from reportlab.platypus import SimpleDocTemplate, Spacer, TableStyle, LongTable
from reportlab.lib.pagesizes import A3
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor

from pdf_exporter.config import config
from pdf_exporter.progress_tracker import ProgressTracker


class Base(ABC):

    DOWNLOAD_LIMIT = 1
    DOWNLOAD_DIR = 'tmp'
    ASSET_DIR = 'assets'
    OUTPUT = 'public/{}_{}.pdf'
    SEG_SIZE = 500
    ROW_PREPAGE = 50

    DASHBOARD = 'de4e4m5u2z5dsa'
    HEADERS = {'Authorization': config['grafana']['authorization']}
    THEME = 'light'
    WIDTH = 1000
    HRIGHT = 500
    CHART_PDF = 'chart.pdf'
    PANELS = []

    def __init__(self):
        now = time.time()
        self.dir = os.path.join(self.DOWNLOAD_DIR, str(int(now)))
        self.pagesize = A3
        self.styles = [
            ('BACKGROUND', (0, 0), (-1, 0), HexColor("#FAFAFA")),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor("#454545")),
            ('TOPPADDING', (0, 0), (-1, 0), 4),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 4),
            ('TOPPADDING', (0, 1), (-1, -1), 2),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 2),
            ('TEXTCOLOR', (0, 1), (-1, -1), HexColor("#595959")),
            ('FONTSIZE', (0, 0), (-1, -1), 8),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 0.5, HexColor("#E8E8E8")),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]
        if not os.path.isdir(self.dir):
            os.makedirs(self.dir)

    async def export(self, source: int, to: int, filename: str = None, tracker: Optional[ProgressTracker] = None):
        """
        Export data to PDF with optional progress tracking.
        
        Args:
            source: Source timestamp
            to: Target timestamp
            filename: Output filename (optional)
            tracker: Progress tracker instance (optional)
            pdfId: PDF ID (optional)

        Returns:
            Path to the generated PDF file
        """
        # Start loading data - 10% progress
        if tracker:
            await tracker.update_progress(10, "loading_data", "Loading data...")
        
        # We need to await _build_tables since we made it async in tracking17B
        input_files = await self._build_tables(source, to, tracker)

        # Start downloading charts - 40% progress
        if tracker:
            await tracker.update_progress(40, "downloading_charts", "Downloading charts...")
        
        charts = await self._build_charts(source, to, tracker)
        input_files = charts + input_files

        # Start merging PDFs - 90% progress
        if tracker:
            await tracker.update_progress(90, "merging_pdf", "Merging PDF files...")
        
        # Ensure the filename doesn't include '.pdf' extension as it's already in the template
        if filename and filename.lower().endswith('.pdf'):
            filename = filename[:-4]
            
        # Format the output path correctly - ensure public directory exists
        output_file = self.OUTPUT.format(str(int(time.time())), filename if filename else str(int(time.time())))
        
        # Make sure the directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        self._merge_pdf(input_files, output_file)
        shutil.rmtree(self.dir)
        
        if tracker:
            await tracker.update_progress(100, "completed", "PDF generation completed", output_file)
        
        return f"/{output_file}"

    def _load_data(self, source: int, to: int):
        """
        Load data from CSV URL with error handling
        
        Returns mock data if real data cannot be loaded from the CSV endpoint
        """
        pd.options.mode.copy_on_write = True
        url = config['csv']['url'].format(source, to)
        
        try:
            df = pd.read_csv(url)
            if not df.empty:
                print(f"Successfully loaded {len(df)} rows of CSV data with columns: {list(df.columns)}")
                return df
            else:
                print("CSV data is empty, using mock data instead")
                from pdf_exporter.mock_data import generate_mock_data
                return generate_mock_data()
        except pd.errors.EmptyDataError as e:
            print(f"Warning: No data available in CSV: {str(e)}")
            print("Using mock data instead")
            from pdf_exporter.mock_data import generate_mock_data
            return generate_mock_data()
        except pd.errors.ParserError as e:
            print(f"Warning: CSV parsing error: {str(e)}")
            print("This might be the 'Invalid Record Length' issue - using mock data instead")
            from pdf_exporter.mock_data import generate_mock_data
            return generate_mock_data()
        except Exception as e:
            print(f"Error loading CSV data: {str(e)}")
            print("Using mock data instead")
            from pdf_exporter.mock_data import generate_mock_data
            return generate_mock_data()

    def _merge_pdf(self, input_files: list[str], output_file: str):
        """Merge multiple PDF files into one with error handling for missing files"""
        pdf_writer = PdfWriter()
        valid_files = []
        
        # First check that all files exist
        for filename in input_files:
            if os.path.exists(filename) and os.path.isfile(filename):
                valid_files.append(filename)
            else:
                print(f"Warning: PDF file not found: {filename} - skipping this file")
        
        # If we have no valid files, create an empty PDF with a message
        if not valid_files:
            print("Error: No valid PDF files to merge. Creating an empty PDF with error message.")
            empty_pdf = f"{self.dir}/empty_result.pdf"
            self._create_empty_pdf(empty_pdf, "No data available to generate this report")
            valid_files = [empty_pdf]
        
        # Now merge the valid files
        for filename in valid_files:
            try:
                pdf_reader = PdfReader(filename)
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    pdf_writer.add_page(page)
            except Exception as e:
                print(f"Error reading PDF file {filename}: {str(e)} - skipping this file")
        
        # Write the merged PDF
        with open(output_file, 'wb') as fh:
            pdf_writer.write(fh)

    async def _build_charts(self, source: int, to: int, tracker: Optional[ProgressTracker] = None):
        """Build chart PDFs with optional progress tracking"""
        if len(self.PANELS) < 1:
            return []
            
        story = []
        total_panels = len(self.PANELS)
        
        if tracker:
            await tracker.update_progress(45, "downloading_charts", f"Downloading charts (0/{total_panels})...")
            
        images = await self.__download_images(self.PANELS, source, to, tracker)
        
        if tracker:
            await tracker.update_progress(60, "processing_charts", "Processing charts...")
            
        for image in images:
            story.append(Image(image, width=self.pagesize[0] - 2 * inch, height=self.HRIGHT / self.WIDTH * (self.pagesize[0] - 2 * inch)))
            story.append(Spacer(1, 0.5*inch))
            
        self._build_pdf(f"{self.dir}/{self.CHART_PDF}", story, topMargin=2 * inch, bottomMargin=1.5 * inch)
        return [f"{self.dir}/{self.CHART_PDF}"]

    @abstractmethod
    async def _build_tables(self, source: int, to: int, tracker: Optional[ProgressTracker] = None):
        """Build table PDFs with optional progress tracking"""
        pass

    def _build_pdf(self, filename: str, story: list, topMargin=1.8 * inch, bottomMargin=3 * inch):
        # Use smaller margins for more compact layout
        leftMargin = 0.5 * inch
        rightMargin = 0.5 * inch
        
        doc = SimpleDocTemplate(
            filename, 
            pagesize=self.pagesize, 
            topMargin=topMargin, 
            bottomMargin=bottomMargin,
            leftMargin=leftMargin,
            rightMargin=rightMargin
        )
        doc.build(story, onFirstPage=self._build_template, onLaterPages=self._build_template)

    def _build_table(self, data: list, col: list[float] = None, styles: list = []):
        total_width = self.pagesize[0] - 2*inch
        style = TableStyle(styles + self.styles)
        colWidths = [item * total_width for item in col] if col is not None else None
        
        # Calculate appropriate row heights based on content
        row_heights = []
        for row in data:
            # Default height for header row
            if row == data[0]:
                row_heights.append(20)  # Slightly taller header row
                continue
                
            # For data rows, calculate height based on content
            max_height = 16  # Minimum row height
            for cell in row:
                # Count number of lines in cell content
                if isinstance(cell, str):
                    lines = cell.count('\n') + 1
                    cell_height = max(lines * 12, 16)  # 12 points per line, minimum 16
                    max_height = max(max_height, cell_height)
            
            row_heights.append(max_height)
        
        # Set maximum row height to prevent excessive spacing
        row_heights = [min(height, 50) for height in row_heights]
        
        return Table(data, repeatRows=1, colWidths=colWidths, 
                        rowHeights=row_heights, style=style)

    async def __download_images(self, panels: list[int], source: int, to: int, tracker: Optional[ProgressTracker] = None):
        """Download images with progress tracking"""
        images = []
        self.connector = aiohttp.TCPConnector(limit=self.DOWNLOAD_LIMIT)
        total_panels = len(panels)
        
        async def download(url: str, panel: int, header: dict, session: aiohttp.ClientSession):
            params = {
                'orgId': 1,
                'theme': self.THEME,
                'width': self.WIDTH,
                'height': self.HRIGHT,
                'from': source,
                'to': to,
                'panelId': f"panel-{panel}"
            }
            image_path = f"{self.dir}/{panel}.png"
            try:
                # Add timeout to prevent hanging on slow responses
                async with session.get(url, headers=header, params=params, timeout=60) as response:
                    if response.status == 504:  # Gateway Time-out
                        print(f"Gateway Time-out error for panel {panel} - generating placeholder image")
                        return await self.__create_placeholder_image(panel, image_path)
                    
                    response.raise_for_status()
                    img_data = await response.read()
                    
                    # Check if we got a valid image (at least 1KB in size)
                    if len(img_data) < 1024:
                        print(f"Warning: Very small response for panel {panel} - generating placeholder")
                        return await self.__create_placeholder_image(panel, image_path)
                        
                    with open(image_path, 'wb') as f:
                        f.write(img_data)
                    return image_path
            except aiohttp.ClientError as e:
                print(f"Client error for panel {panel}: {str(e)} - generating placeholder")
                return await self.__create_placeholder_image(panel, image_path)
            except asyncio.TimeoutError:
                print(f"Timeout downloading panel {panel} - generating placeholder")
                return await self.__create_placeholder_image(panel, image_path)
            except Exception as e:
                print(f"Unexpected error for panel {panel}: {str(e)} - generating placeholder")
                return await self.__create_placeholder_image(panel, image_path)

        url = f"{config['grafana']['url']}{self.DASHBOARD}"

        async with aiohttp.ClientSession(connector=self.connector) as session:
            for i, panel in enumerate(panels):
                try:
                    # Update progress - incrementally from 45% to 60% based on download progress
                    if tracker and total_panels > 0:
                        progress = 45 + (i / total_panels) * 15
                        await tracker.update_progress(
                            int(progress), 
                            "downloading_charts", 
                            f"Downloading charts ({i}/{total_panels})..."
                        )
                    
                    image_path = await download(url, panel, self.HEADERS, session)
                    images.append(image_path)
                except Exception as e:
                    print('get failed:', panel, e)
        
        return images

    def _build_template(self, canvas, doc):
        scale = self.pagesize[0] / 1653
        canvas.saveState()
        canvas.drawImage(f"{self.ASSET_DIR}/logo.png", x=inch, y=self.pagesize[1] - 100, width=259 * scale, height=105 * scale, mask='auto')
        canvas.setFont('Helvetica', 10)
        canvas.setFillColor(HexColor("#AAA8A9"))
        canvas.setStrokeColor(HexColor("#AAA8A9"))
        canvas.line(640, self.pagesize[1] - 55, 640, self.pagesize[1] - 97)
        canvas.drawString(540, self.pagesize[1] - 70, "t. +1 267 607 3375")
        canvas.drawString(540, self.pagesize[1] - 90, "e. <EMAIL>")
        canvas.drawString(650, self.pagesize[1] - 70, "2450 Holcombe Blvd Suite")
        canvas.drawString(650, self.pagesize[1] - 90, "XHouston, TX 77021")

        canvas.drawImage(f"{self.ASSET_DIR}/footer.png", 0, 0, width=self.pagesize[0], height=419 * scale, mask='auto')
        canvas.setFillColor(HexColor("#FFFFFF"))
        copyright = "© 2025 Regdesk, Inc. All rights reserved"
        canvas.drawCentredString(self.pagesize[0] / 2, 45, copyright)
        canvas.restoreState()

    STATUS = {
        0: 'Requested',
        1: 'Preparing',
        2: 'Paused',
        3: 'Internal',
        4: 'Government',
        5: 'Information',
        6: 'Rejected',
        7: 'Approved',
        8: 'Discontinue',
        9: 'Renewal',
        10: 'Clinical',
        11: 'Lab Testing',
        12: 'State/Provincial Approval',
        13: 'Submitted',
        14: 'Approved Ongoing',
        15: 'Renewal Ongoing',
    }

    def _get_status(self, val: int) -> str:
        return self.STATUS[val]

    def _cut_str(self, text: str, size: float) -> str:
        # return text.replace('(.{len * 16})', r'\1\n', regex=True)
        length = int(size * 160) - 1
        ext = '…' if len(text) > length else ''
        return text[:length] + ext

    def _get_indexes(self, grouped):
        start = 0
        g = []
        for row in grouped:
            end = row + start - 1
            start_step = start // self.ROW_PREPAGE
            end_step = end // self.ROW_PREPAGE
            if start_step < end_step:
                for step in range(start_step, end_step):
                    tmp = (step + 1) * self.ROW_PREPAGE
                    g.append((start, tmp - 1))
                    start = tmp
                g.append((start, end))
            else:
                if row > 1:
                    g.append((start, end))
            start = end + 1
        return g
        
    def _format_column_header(self, column_name: str) -> str:
        import re
        formatted = re.sub(r'(?<!^)(?=[A-Z])', ' ', column_name)
        return formatted.title()
    
    def _format_headers(self, columns: list) -> list:
        return [self._format_column_header(col) for col in columns]
        
    async def __create_placeholder_image(self, panel_id, image_path):
        """Create a placeholder image when chart download fails"""
        import asyncio
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a blank image with the same dimensions
        width, height = self.WIDTH, self.HRIGHT
        image = Image.new('RGB', (width, height), color=(240, 240, 240))
        draw = ImageDraw.Draw(image)
        
        # Add some text explaining the issue
        try:
            # Try to load a font, fall back to default if not available
            font = ImageFont.truetype("Arial", 24)
        except IOError:
            font = ImageFont.load_default()
            
        # Draw panel ID and error message
        message = f"Panel {panel_id} unavailable"
        error_msg = "Chart could not be loaded from Grafana"
        contact_msg = "Please check Grafana server or try again later"
        
        # Calculate text positions
        text_width, text_height = draw.textbbox((0, 0), message, font=font)[2:4]
        error_width, error_height = draw.textbbox((0, 0), error_msg, font=font)[2:4]
        contact_width, contact_height = draw.textbbox((0, 0), contact_msg, font=font)[2:4]
        
        # Draw text centered on the image
        draw.text(((width - text_width) // 2, height // 2 - 40), message, fill=(100, 100, 100), font=font)
        draw.text(((width - error_width) // 2, height // 2), error_msg, fill=(100, 100, 100), font=font)
        draw.text(((width - contact_width) // 2, height // 2 + 40), contact_msg, fill=(100, 100, 100), font=font)
        
        # Draw a border
        draw.rectangle([(0, 0), (width-1, height-1)], outline=(200, 200, 200))
        
        # Save the image
        image.save(image_path)
        return image_path
    
    def _create_empty_pdf(self, filename, message="No data available"):
        """Create a simple PDF with a message for empty data cases"""
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.platypus import Paragraph
        
        styles = getSampleStyleSheet()
        style = styles['Heading1']
        
        # Create a paragraph with the message
        p = Paragraph(message, style)
        
        # Build PDF with the message
        self._build_pdf(filename, [p])
