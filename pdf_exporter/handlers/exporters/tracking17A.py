import pandas as pd
from typing import Optional, List

from pdf_exporter.handlers.exporters.base import Base
from pdf_exporter.config import config
from pdf_exporter.progress_tracker import ProgressTracker


class Handler(Base):

    PANELS = [61, 62, 63, 64, 65]

    async def _build_tables(self, source: int, to: int, tracker: Optional[ProgressTracker] = None):
        """Build table PDFs with optional progress tracking"""
        input_files = []
        
        # Start data loading - progress 10-15%
        if tracker:
            await tracker.update_progress(10, "loading_data", "正在加载CSV数据...")
            
        df = self._load_data(source, to)
        
        if tracker:
            await tracker.update_progress(15, "processing_data", "正在处理数据...")
            
        # Check if we have data to process
        if df.empty:
            print("Warning: No data available for the requested time range")
            
            # Generate a blank PDF with a message instead of returning an empty list
            message_pdf = f"{self.dir}/empty.pdf"
            self._create_empty_pdf(message_pdf, "No data available for the requested time range")
            
            if tracker:
                await tracker.update_progress(35, "tables_completed", "无数据可用")
                
            return [message_pdf]

# Build first set of tables - progress 15-25%

        if tracker:
            await tracker.update_progress(20, "generating_pdfs", "正在生成PDF表格...")

        # Generate PDFs with progress tracking
        segments = [df.iloc[seg:seg + self.SEG_SIZE] for seg in range(0, len(df), self.SEG_SIZE)]
        for index, item in enumerate(segments):
            # Update progress incrementally from 20% to 35%
            if tracker and len(segments) > 0:
                progress = 20 + (index / len(segments)) * 15
                await tracker.update_progress(
                    int(progress), 
                    "generating_pdfs", 
                    f"正在生成PDF表格 ({index+1}/{len(segments)})..."
                )
                
            file = f"{self.dir}/{index}.pdf"
            self.generate_pdf(item, file)
            input_files.append(file)
            
        if tracker:
            await tracker.update_progress(35, "tables_completed", "表格处理完成")
        return input_files

    def generate_pdf(self, df, filename="output.pdf"):
        """Generate a PDF file with the provided data"""
        if df.empty:
            self._create_empty_pdf(filename, "No data available for this segment")
            return
            
        # Filter and prepare data
        df = df[['productFamily', 'product', 'country', 'status', 'trackingId']]
        df = df.dropna(subset=['trackingId'], how='any')
        
        # Format status if it's a number
        def format_status(status):
            try:
                status_num = int(float(status))
                if status_num in self.STATUS:
                    return self.STATUS[status_num]
            except (ValueError, TypeError):
                pass
            return str(status)
            
        df['status'] = df['status'].apply(format_status)
        
        # Apply formatting to strings
        df.fillna({'productFamily': '', 'product': '', 'country': '', 'status': 'Unknown'}, inplace=True)
        df['productFamily'] = df['productFamily'].astype(str).apply(lambda x: self._cut_str(x, .25))
        df['product'] = df['product'].astype(str).apply(lambda x: self._cut_str(x, .25))
        df['country'] = df['country'].astype(str).apply(lambda x: self._cut_str(x, .15))
        
        # Sort the data
        df = df.sort_values(['productFamily', 'product', 'country', 'status'])
        df = df.reset_index(drop=True)

        # Create the table
        product_family_grouped = self._get_indexes(df.groupby('productFamily', sort=False).size())
        product_grouped = self._get_indexes(df.groupby(['productFamily', 'product'], sort=False).size())
        country_grouped = self._get_indexes(df.groupby(['productFamily', 'product', 'country'], sort=False).size())

        styles = []
        for row in product_family_grouped:
            styles.append(('SPAN', (0, row[0] + 1), (0, row[1] + 1)))
        for row in product_grouped:
            styles.append(('SPAN', (1, row[0] + 1), (1, row[1] + 1)))
        for row in country_grouped:
            styles.append(('SPAN', (2, row[0] + 1), (2, row[1] + 1)))

        df.drop(['trackingId'], axis=1, inplace=True)
        data = df.values.tolist()
        data.insert(0, self._format_headers(df.columns.tolist()))

        table = self._build_table(data, col=[.30, .30, .20, .20], styles=styles)
        self._build_pdf(filename, [table])

    def _create_empty_pdf(self, filename, message="No data available"):
        """Create a simple PDF with a message for empty data cases"""
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.platypus import Paragraph
        
        styles = getSampleStyleSheet()
        style = styles['Heading1']
        
        # Create a paragraph with the message
        p = Paragraph(message, style)
        
        # Build PDF with the message
        self._build_pdf(filename, [p])
