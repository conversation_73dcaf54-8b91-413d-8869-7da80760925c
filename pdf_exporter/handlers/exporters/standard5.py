import pandas as pd
from typing import Optional, List

from pdf_exporter.handlers.exporters.base import Base
from pdf_exporter.config import config
from pdf_exporter.progress_tracker import ProgressTracker


class Handler(Base):

    PANELS = [18, 19, 20]

    async def _build_tables(self, source: int, to: int, tracker: Optional[ProgressTracker] = None):
        """Build table PDFs with optional progress tracking"""
        input_files = []
        
        # Start data loading - progress 10-15%
        if tracker:
            await tracker.update_progress(10, "loading_data", "正在加载CSV数据...")
            
        df = self._load_data(source, to)
        
        if tracker:
            await tracker.update_progress(15, "processing_data", "正在处理数据...")
            
        # Check if we have data to process
        if df.empty:
            print("Warning: No data available for the requested time range")
            
            # Generate a blank PDF with a message instead of returning an empty list
            message_pdf = f"{self.dir}/empty.pdf"
            self._create_empty_pdf(message_pdf, "No data available for the requested time range")
            
            if tracker:
                await tracker.update_progress(35, "tables_completed", "无数据可用")
                
            return [message_pdf]

        df = df[['oldVersion', 'newVersion', 'updateDate']]
        df = df.fillna('')
        df['oldVersion'] = df['oldVersion'].astype(str).apply(lambda x: self._cut_str(x, .4))
        df['newVersion'] = df['newVersion'].astype(str).apply(lambda x: self._cut_str(x, .4))
        df['updateDate'] = df['updateDate'].astype(str).apply(lambda x: self._cut_str(x, .2))

        if tracker:
            await tracker.update_progress(20, "generating_pdfs", "正在生成PDF表格...")

        # Generate PDFs with progress tracking
        segments = [df.iloc[seg:seg + self.SEG_SIZE] for seg in range(0, len(df), self.SEG_SIZE)]
        for index, item in enumerate(segments):
            # Update progress incrementally from 20% to 35%
            if tracker and len(segments) > 0:
                progress = 20 + (index / len(segments)) * 15
                await tracker.update_progress(
                    int(progress), 
                    "generating_pdfs", 
                    f"正在生成PDF表格 ({index+1}/{len(segments)})..."
                )
                
            file = f"{self.dir}/{index}.pdf"
            self.generate_pdf(item, file)
            input_files.append(file)
            
        if tracker:
            await tracker.update_progress(35, "tables_completed", "表格处理完成")
        return input_files

    def generate_pdf(self, df, filename="output.pdf"):
        data = df.values.tolist()
        data.insert(0, self._format_headers(df.columns.tolist()))  # Fixed height for all rows
        table = self._build_compact_table(data, [.4, .4, .2])
        self._build_pdf(filename, [table])
        
    def _build_compact_table(self, data, col=None, styles=[], row_heights=None):
        """Build a compact table with dynamic row heights based on content"""
        from reportlab.platypus import Table, TableStyle
        from reportlab.lib.units import inch
        
        total_width = self.pagesize[0] - 2*inch
        style = TableStyle(styles + self.styles)
        colWidths = [item * total_width for item in col] if col is not None else None
        
        # Calculate appropriate row heights based on content if not provided
        if not row_heights:
            row_heights = []
            for row_idx, row in enumerate(data):
                # Header row gets slightly more space
                if row_idx == 0:
                    row_heights.append(12)
                    continue
                
                # For data rows, calculate height based on content
                max_height = 0
                for cell in row:
                    # Count approximate number of lines needed for this cell
                    if isinstance(cell, str):
                        # Calculate how many lines this text would need
                        text_len = len(cell)
                        # Rough estimate - assume 10 chars per line for each 10% of column width
                        # This is a simple approximation that can be adjusted
                        cell_col_idx = row.index(cell)
                        if cell_col_idx < len(col) and col:
                            col_width_factor = col[cell_col_idx]
                            chars_per_line = int(col_width_factor * 100)
                            lines = max(1, text_len / chars_per_line) if chars_per_line > 0 else 1
                            # Calculate height: base 8pt font with 2pt line spacing
                            cell_height = int(8 * lines) + 2
                            max_height = max(max_height, cell_height)
                
                # Set minimum height and avoid excessive heights
                row_heights.append(max(10, min(max_height, 30)))
        
        # Add special style to enable auto word wrapping
        # The style command needs to be in the correct format for ReportLab
        if ('WORDWRAP', (0, 0), (-1, -1)) not in style._cmds:
            style.add('WORDWRAP', (0, 0), (-1, -1))
        
        # Use Table instead of LongTable for more compact layout with dynamic row heights
        table = Table(data, 
                     repeatRows=1, 
                     colWidths=colWidths, 
                     rowHeights=row_heights,
                     style=style)
        
        return table
        
    def _create_empty_pdf(self, filename, message="No data available"):
        """Create a simple PDF with a message for empty data cases"""
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.platypus import Paragraph
        
        styles = getSampleStyleSheet()
        style = styles['Heading1']
        
        # Create a paragraph with the message
        p = Paragraph(message, style)
        
        # Build PDF with the message
        self._build_pdf(filename, [p])
