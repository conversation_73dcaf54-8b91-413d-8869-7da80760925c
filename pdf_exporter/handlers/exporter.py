from fastapi import HTTPException
from typing import Optional

from pdf_exporter.handlers.base import Base
from pdf_exporter.handlers.exporters import *
from pdf_exporter.progress_tracker import ProgressTracker


class Handler(Base):
    async def export(self, dashboard: str, source: int, to: int, filename: str = None, tracker: Optional[ProgressTracker] = None):
        match dashboard:
            case 'tracking1':
                return await tracking1.Handler().export(source, to, filename, tracker)
            case 'tracking2':
                return await tracking2.Handler().export(source, to, filename, tracker)
            case 'tracking3':
                return await tracking3.Handler().export(source, to, filename, tracker)
            case 'tracking4':
                return await tracking4.Handler().export(source, to, filename, tracker)
            case 'tracking5':
                return await tracking5.Handler().export(source, to, filename, tracker)
            case 'tracking6':
                return await tracking6.Handler().export(source, to, filename, tracker)
            case 'tracking7':
                return await tracking7.Handler().export(source, to, filename, tracker)
            case 'tracking8':
                return await tracking8.Handler().export(source, to, filename, tracker)
            case 'tracking9':
                return await tracking9.Handler().export(source, to, filename, tracker)
            case 'tracking10':
                return await tracking10.Handler().export(source, to, filename, tracker)
            case 'tracking11':
                return await tracking11.Handler().export(source, to, filename, tracker)
            case 'tracking12':
                return await tracking12.Handler().export(source, to, filename, tracker)
            case 'tracking13':
                return await tracking13.Handler().export(source, to, filename, tracker)
            case 'tracking14':
                return await tracking14.Handler().export(source, to, filename, tracker)
            case 'tracking15':
                return await tracking15.Handler().export(source, to, filename, tracker)
            case 'tracking16':
                return await tracking16.Handler().export(source, to, filename, tracker)
            case 'tracking17A':
                return await tracking17A.Handler().export(source, to, filename, tracker)
            case 'tracking17B':
                return await tracking17B.Handler().export(source, to, filename, tracker)
            case 'tracking18':
                return await tracking18.Handler().export(source, to, filename, tracker)
            case 'tracking19':
                return await tracking19.Handler().export(source, to, filename, tracker)
            case 'standard1':
                return await standard1.Handler().export(source, to, filename, tracker)
            case 'standard2':
                return await standard2.Handler().export(source, to, filename, tracker)
            case 'standard3':
                return await standard3.Handler().export(source, to, filename, tracker)
            case 'standard4':
                return await standard4.Handler().export(source, to, filename, tracker)
            case 'standard5':
                return await standard5.Handler().export(source, to, filename, tracker)
            case 'standard6':
                return await standard6.Handler().export(source, to, filename, tracker)
            case 'dct1':
                return await dct1.Handler().export(source, to, filename, tracker)
            case 'dct2':
                return await dct2.Handler().export(source, to, filename, tracker)
            case 'dct3':
                return await dct3.Handler().export(source, to, filename, tracker)
            case 'product1':
                return await product1.Handler().export(source, to, filename, tracker)
            case 'product2':
                return await product2.Handler().export(source, to, filename, tracker)
            case 'product3':
                return await product3.Handler().export(source, to, filename, tracker)
            case _:
                raise HTTPException(status_code=404, detail="invalid dashboard")
