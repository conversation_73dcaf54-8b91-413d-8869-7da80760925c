[tool.poetry]
name = "pdf-exporter"
version = "0.1.0"
description = ""
authors = ["howe <shi<PERSON>@regdesk.co>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.13"
fastapi = "^0.115.5"
uvicorn = "^0.32.1"
pyyaml = "^6.0.2"
requests = "^2.32.3"
pandas = "^2.2.3"
reportlab = "^4.2.5"
aiohttp = "^3.11.7"
unidecode = "^1.3.8"
pypdf = "^5.1.0"
pillow = "^10.0.0"  # Added for placeholder image generation

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.3"
pytest-html = "^4.1.1"
coverage = "^7.6.7"
pytest-memray = "^1.7.0"
httpx = "^0.28.1"
pyinstrument = "^5.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
launch = "pdf_exporter.main:main"

[tool.pytest.ini_options]
addopts = "--memray"
