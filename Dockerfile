FROM python

EXPOSE 80

RUN mkdir -p /work && mkdir -p -m 0700 ~/.ssh && ssh-keyscan bitbucket.org >> ~/.ssh/known_hosts

# 使用挂载的 SSH agent 或密钥克隆
RUN --mount=type=ssh <NAME_EMAIL>:regdesk-dev/regdesk-grafana.git -b dev /work

RUN cd /work && pip install poetry && poetry update

Add ./config.yaml /work

WORKDIR /work

CMD ["poetry run launch"]

# ssh-add ~/.ssh/id_rsa
# DOCKER_BUILDKIT=1 docker build --ssh default -t 748128147878.dkr.ecr.eu-central-1.amazonaws.com/regdesk/pdf-exporter .